apiVersion: apps/v1
kind: Deployment
metadata:
  name: gameimageproxydeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gameimageproxy
  template:
    metadata:
      labels:
        app: gameimageproxy
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gameimageproxycontainer
          image: blockchainregistry.azurecr.io/remonster_image_proxy:latest
          imagePullPolicy: "Always"
          env:
          - name: IMAGEPROXY_CACHE
            value: "azure://cacheimg/"
          - name: AZURESTORAGE_ACCOUNT_NAME
            valueFrom:
              secretKeyRef:
                name: azure-storage-secret
                key: AZURESTORAGE_ACCOUNT_NAME
          - name: AZURESTORAGE_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                name: azure-storage-secret
                key: AZURESTORAGE_ACCESS_KEY
          ports:
            - containerPort: 8080
          resources:
            limits:
              cpu: "500m"
              memory: "4Gi"
            requests:
              cpu: "200m"
              memory: "2Gi"
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gameimageproxyservice
  namespace: public-ingress
spec:
  selector:
    app: gameimageproxy
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
