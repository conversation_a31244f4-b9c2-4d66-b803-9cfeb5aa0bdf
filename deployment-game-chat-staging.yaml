apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamechatstagingdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamechatstaging
  template:
    metadata:
      labels:
        app: gamechatstaging
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamechatstagingcontainer
          image: blockchainregistry.azurecr.io/game-chat:staging
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamechatstagingservice
  namespace: public-ingress
spec:
  selector:
    app: gamechatstaging
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP