# Mount file share as a persistent volume
# Create a new file named azurefiles-pv.yaml and copy in the following contents. Under csi, update resourceGroup, volumeHandle, and shareName.
# For mount options, the default value for fileMode and dirMode is 0777.
# Create the persistent volume using the kubectl create command.
apiVersion: v1
kind: PersistentVolume
metadata:
  annotations:
    pv.kubernetes.io/provisioned-by: file.csi.azure.com
  name: azurefile-game-deploy
  namespace: public-ingress
spec:
  capacity:
    storage: 5Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: azurefile-csi
  csi:
    driver: file.csi.azure.com
    volumeHandle: "{GameServerAPI}#{remonster}#{remonster-deploy}"  # make sure this volumeid is unique for every identical share in the cluster
    volumeAttributes:
      resourceGroup: GameServerAPI  # optional, only set this when storage account is not in the same resource group as node
      shareName: remonster-deploy
    nodeStageSecretRef:
      name: azure-secret
      namespace: default
  mountOptions:
    - dir_mode=0777
    - file_mode=0777
    - uid=0
    - gid=0
    - mfsymlinks
    - cache=strict
    - nosharesock
    - nobrl