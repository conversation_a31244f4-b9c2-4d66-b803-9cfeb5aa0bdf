apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamelobbytestdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamelobbytest
  template:
    metadata:
      labels:
        app: gamelobbytest
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamelobbytestcontainer
          image: blockchainregistry.azurecr.io/remonster_lobby_server:test
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamelobbytestservice
  namespace: public-ingress
spec:
  selector:
    app: gamelobbytest
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP