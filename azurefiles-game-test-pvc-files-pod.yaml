kind: Pod
apiVersion: v1
metadata:
  name: game-test
  namespace: public-ingress
spec:
  containers:
    - name: game-test
      image: mcr.microsoft.com/oss/nginx/nginx:1.15.5-alpine
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 250m
          memory: 256Mi
      volumeMounts:
        - mountPath: /usr/share/nginx/html
          name: game-test
          readOnly: false
  volumes:
   - name: game-test
     persistentVolumeClaim:
       claimName: azurefile-game-test