apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamelobbystagingdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamelobbystaging
  template:
    metadata:
      labels:
        app: gamelobbystaging
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamelobbystagingcontainer
          image: blockchainregistry.azurecr.io/remonster_lobby_server:staging
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamelobbystagingservice
  namespace: public-ingress
spec:
  selector:
    app: gamelobbystaging
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP