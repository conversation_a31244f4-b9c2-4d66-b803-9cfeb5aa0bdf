apiVersion: apps/v1
kind: Deployment
metadata:
  name: gameapistagingdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gameapistaging
  template:
    metadata:
      labels:
        app: gameapistaging
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gameapistagingcontainer
          image: blockchainregistry.azurecr.io/game-api-staging:latest
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 250m
              memory: 512Mi
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: thinhda2
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: gameapistagingservice
  namespace: public-ingress
spec:
  selector:
    app: gameapistaging
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP