kind: Pod
apiVersion: v1
metadata:
  name: game-stagging
  namespace: public-ingress
spec:
  containers:
    - name: game-stagging
      image: mcr.microsoft.com/oss/nginx/nginx:1.15.5-alpine
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 250m
          memory: 256Mi
      volumeMounts:
        - mountPath: /usr/share/nginx/html
          name: game-stagging
          readOnly: false
  volumes:
   - name: game-stagging
     persistentVolumeClaim:
       claimName: azurefile-game-stagging