apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamestagingdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamestaging
  template:
    metadata:
      labels:
        app: gamestaging
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamestagingcontainer
          image: blockchainregistry.azurecr.io/nginx_game_image:latest
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - mountPath: /usr/share/nginx/html
              name: gamestagingcontainer
              readOnly: false
      volumes:
        - name: gamestagingcontainer
          persistentVolumeClaim:
            claimName: azurefile-game-stagging
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamestagingservice
  namespace: public-ingress
spec:
  selector:
    app: gamestaging
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
