apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamebetadeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamebeta
  template:
    metadata:
      labels:
        app: gamebeta
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamebetacontainer
          image: blockchainregistry.azurecr.io/nginx_game_image:latest
          volumeMounts:
            - mountPath: /usr/share/nginx/html
              name: gamebetacontainer
              readOnly: false
      volumes:
        - name: gamebetacontainer
          persistentVolumeClaim:
            claimName: azurefile-game-deploy
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamebetaservice
  namespace: public-ingress
spec:
  selector:
    app: gamebeta
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP