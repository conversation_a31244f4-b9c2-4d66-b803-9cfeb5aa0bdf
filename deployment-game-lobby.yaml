apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamelobbydeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamelobby
  template:
    metadata:
      labels:
        app: gamelobby
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamelobbycontainer
          image: blockchainregistry.azurecr.io/remonster_lobby_server:latest
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 8080
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamelobbyservice
  namespace: public-ingress
spec:
  selector:
    app: gamelobby
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP