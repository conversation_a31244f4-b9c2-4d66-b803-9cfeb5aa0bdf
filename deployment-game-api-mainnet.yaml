apiVersion: apps/v1
kind: Deployment
metadata:
  name: gameapimainnetdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gameapimainnet
  template:
    metadata:
      labels:
        app: gameapimainnet
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gameapimainnetcontainer
          image: blockchainregistry.azurecr.io/game-api-mainnet:latest
          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: 1000m
              memory: 2Gi
          imagePullPolicy: "Always"
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: thinhda2
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: gameapimainnetservice
  namespace: public-ingress
spec:
  selector:
    app: gameapimainnet
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP

