apiVersion: apps/v1
kind: Deployment
metadata:
  name: gamechattestdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gamechattest
  template:
    metadata:
      labels:
        app: gamechattest
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gamechattestcontainer
          image: blockchainregistry.azurecr.io/game-chat:test
          imagePullPolicy: "Always"
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gamechattestservice
  namespace: public-ingress
spec:
  selector:
    app: gamechattest
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP