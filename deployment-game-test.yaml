apiVersion: apps/v1
kind: Deployment
metadata:
  name: gametestdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gametest
  template:
    metadata:
      labels:
        app: gametest
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gametestcontainer
          image: blockchainregistry.azurecr.io/nginx_game_image:latest
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 250m
              memory: 256Mi
          volumeMounts:
            - mountPath: /usr/share/nginx/html
              name: gametestcontainer
              readOnly: false
      volumes:
        - name: gametestcontainer
          persistentVolumeClaim:
            claimName: azurefile-game-test
      imagePullSecrets:
        - name: thinhda2
---
apiVersion: v1
kind: Service
metadata:
  name: gametestservice
  namespace: public-ingress
spec:
  selector:
    app: gametest
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP