apiVersion: apps/v1
kind: Deployment
metadata:
  name: gameapitestnetdeployment
  namespace: public-ingress
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gameapitestnet
  template:
    metadata:
      labels:
        app: gameapitestnet
    spec:
      terminationGracePeriodSeconds: 30
      containers:
        - name: gameapitestnetcontainer
          image: blockchainregistry.azurecr.io/game-api-testnet:latest
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 250m
              memory: 512Mi
          imagePullPolicy: "Always"
          ports:
            - containerPort: 80
      imagePullSecrets:
        - name: thinhda2
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: gameapitestnetservice
  namespace: public-ingress
spec:
  selector:
    app: gameapitestnet
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP